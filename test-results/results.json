{"config": {"configFile": "/Users/<USER>/Github/ns-shop/playwright.config.ts", "rootDir": "/Users/<USER>/Github/ns-shop/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/ns-shop/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/ns-shop/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 3}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/ns-shop/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/ns-shop/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/ns-shop/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/ns-shop/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/ns-shop/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/ns-shop/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/ns-shop/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/ns-shop/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/ns-shop/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 3}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/ns-shop/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "smoke.spec.ts", "file": "smoke.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Smoke Tests", "file": "smoke.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should load the application homepage", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8171, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-07T10:58:28.833Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4219922fea2e2bd3c691-1f35000f8c05a14607f3", "file": "smoke.spec.ts", "line": 4, "column": 7}, {"title": "should navigate to admin login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 8055, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-07T10:58:28.887Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4219922fea2e2bd3c691-adce7e99eb7a5229b59d", "file": "smoke.spec.ts", "line": 9, "column": 7}, {"title": "should show demo credentials on login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 8023, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-07T10:58:28.862Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4219922fea2e2bd3c691-d41cc6ad68089a529af4", "file": "smoke.spec.ts", "line": 15, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-07-07T10:58:25.389Z", "duration": 15696.026, "expected": 3, "skipped": 0, "unexpected": 0, "flaky": 0}}