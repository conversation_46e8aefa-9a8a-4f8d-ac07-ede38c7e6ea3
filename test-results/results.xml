<testsuites id="" name="" tests="3" failures="0" skipped="0" errors="0" time="15.696026">
<testsuite name="smoke.spec.ts" timestamp="2025-07-07T10:58:27.737Z" hostname="chromium" tests="3" failures="0" skipped="0" time="24.249" errors="0">
<testcase name="Smoke Tests › should load the application homepage" classname="smoke.spec.ts" time="8.171">
</testcase>
<testcase name="Smoke Tests › should navigate to admin login page" classname="smoke.spec.ts" time="8.055">
</testcase>
<testcase name="Smoke Tests › should show demo credentials on login page" classname="smoke.spec.ts" time="8.023">
</testcase>
</testsuite>
</testsuites>