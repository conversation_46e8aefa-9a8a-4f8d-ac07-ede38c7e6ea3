import { describe, it, expect, beforeEach, afterEach } from "@jest/globals";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { SessionProvider } from "next-auth/react";
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import AdminSignIn from "@/app/admin/auth/signin/page";
import AdminUsersPage from "@/app/admin/admins/page";

const prisma = new PrismaClient();

// Mock next/navigation
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    refresh: jest.fn(),
  }),
}));

// Mock next-auth/react
jest.mock("next-auth/react", () => ({
  signIn: jest.fn(),
  getSession: jest.fn(),
  useSession: jest.fn(),
  SessionProvider: ({ children }: { children: React.ReactNode }) => children,
}));

const { signIn, getSession, useSession } = require("next-auth/react");

describe("Admin Authentication Flow", () => {
  beforeEach(async () => {
    // Clean up before each test
    await prisma.adminUser.deleteMany();
    jest.clearAllMocks();
  });

  afterEach(async () => {
    // Clean up after each test
    await prisma.adminUser.deleteMany();
  });

  describe("Admin Sign In Page", () => {
    it("should render admin sign in form", () => {
      render(<AdminSignIn />);

      (expect(screen.getByText("Admin Portal")) as any).toBeInTheDocument();
      (
        expect(
          screen.getByText("Đăng nhập vào hệ thống quản trị NS Shop")
        ) as any
      ).toBeInTheDocument();
      (expect(screen.getByLabelText("Email")) as any).toBeInTheDocument();
      (expect(screen.getByLabelText("Mật khẩu")) as any).toBeInTheDocument();
      (
        expect(screen.getByRole("button", { name: "Đăng nhập" })) as any
      ).toBeInTheDocument();
    });

    it("should show demo credentials", () => {
      render(<AdminSignIn />);

      (expect(screen.getByText("Tài khoản demo:")) as any).toBeInTheDocument();
      (
        expect(screen.getByText("Admin: <EMAIL> / admin123")) as any
      ).toBeInTheDocument();
      (
        expect(
          screen.getByText("Moderator: <EMAIL> / moderator123")
        ) as any
      ).toBeInTheDocument();
    });

    it("should handle form submission", async () => {
      signIn.mockResolvedValue({ ok: true });
      getSession.mockResolvedValue({
        user: { id: "1", type: "admin", role: "ADMIN" },
      });

      render(<AdminSignIn />);

      const emailInput = screen.getByLabelText("Email");
      const passwordInput = screen.getByLabelText("Mật khẩu");
      const submitButton = screen.getByRole("button", { name: "Đăng nhập" });

      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "password123" } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(signIn).toHaveBeenCalledWith("admin-credentials", {
          email: "<EMAIL>",
          password: "password123",
          redirect: false,
          callbackUrl: "/admin",
        });
      });
    });

    it("should show error for invalid credentials", async () => {
      signIn.mockResolvedValue({ error: "CredentialsSignin" });

      render(<AdminSignIn />);

      const emailInput = screen.getByLabelText("Email");
      const passwordInput = screen.getByLabelText("Mật khẩu");
      const submitButton = screen.getByRole("button", { name: "Đăng nhập" });

      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "wrongpassword" } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        (
          expect(screen.getByText("Email hoặc mật khẩu không đúng")) as any
        ).toBeInTheDocument();
      });
    });

    it("should show error for non-admin user", async () => {
      signIn.mockResolvedValue({ ok: true });
      getSession.mockResolvedValue({
        user: { id: "1", type: "user", role: "USER" },
      });

      render(<AdminSignIn />);

      const emailInput = screen.getByLabelText("Email");
      const passwordInput = screen.getByLabelText("Mật khẩu");
      const submitButton = screen.getByRole("button", { name: "Đăng nhập" });

      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "password123" } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        (
          expect(
            screen.getByText("Tài khoản không có quyền truy cập admin")
          ) as any
        ).toBeInTheDocument();
      });
    });
  });

  describe("Admin Users Page Access Control", () => {
    it("should show access denied for moderator users", () => {
      useSession.mockReturnValue({
        data: {
          user: { id: "1", type: "admin", role: "MODERATOR" },
        },
      });

      render(<AdminUsersPage />);

      (
        expect(screen.getByText("Không có quyền truy cập")) as any
      ).toBeInTheDocument();
      (
        expect(
          screen.getByText(
            "Chỉ Admin mới có quyền quản lý tài khoản quản trị viên"
          )
        ) as any
      ).toBeInTheDocument();
    });

    it("should show admin management interface for admin users", () => {
      useSession.mockReturnValue({
        data: {
          user: { id: "1", type: "admin", role: "ADMIN" },
        },
      });

      // Mock fetch for API calls
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () =>
          Promise.resolve({
            adminUsers: [],
            pagination: { page: 1, limit: 20, total: 0, pages: 0 },
          }),
      });

      render(<AdminUsersPage />);

      (expect(screen.getByText("Quản lý Admin")) as any).toBeInTheDocument();
      (
        expect(
          screen.getByText("Quản lý tài khoản quản trị viên và phân quyền")
        ) as any
      ).toBeInTheDocument();
      (
        expect(screen.getByRole("button", { name: "Thêm Admin" })) as any
      ).toBeInTheDocument();
    });

    it("should display admin statistics cards", () => {
      useSession.mockReturnValue({
        data: {
          user: { id: "1", type: "admin", role: "ADMIN" },
        },
      });

      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () =>
          Promise.resolve({
            adminUsers: [
              { id: "1", role: "ADMIN", isActive: true },
              { id: "2", role: "MODERATOR", isActive: true },
              { id: "3", role: "ADMIN", isActive: false },
            ],
            pagination: { page: 1, limit: 20, total: 3, pages: 1 },
          }),
      });

      render(<AdminUsersPage />);

      (expect(screen.getByText("Tổng Admin")) as any).toBeInTheDocument();
      (expect(screen.getByText("Moderator")) as any).toBeInTheDocument();
      (expect(screen.getByText("Hoạt động")) as any).toBeInTheDocument();
    });
  });

  describe("Admin Authentication Database Integration", () => {
    it("should authenticate admin user from database", async () => {
      // Create test admin user
      const hashedPassword = await bcrypt.hash("testpassword", 12);
      const adminUser = await prisma.adminUser.create({
        data: {
          email: "<EMAIL>",
          name: "Test Admin",
          password: hashedPassword,
          role: "ADMIN",
          isActive: true,
        },
      });

      // Simulate authentication
      const foundUser = await prisma.adminUser.findUnique({
        where: { email: "<EMAIL>" },
      });

      expect(foundUser).toBeDefined();
      expect(foundUser?.email).toBe("<EMAIL>");
      expect(foundUser?.role).toBe("ADMIN");
      expect(foundUser?.isActive).toBe(true);

      // Verify password
      const isPasswordValid = await bcrypt.compare(
        "testpassword",
        foundUser!.password
      );
      expect(isPasswordValid).toBe(true);
    });

    it("should update lastLoginAt on successful login", async () => {
      const hashedPassword = await bcrypt.hash("testpassword", 12);
      const adminUser = await prisma.adminUser.create({
        data: {
          email: "<EMAIL>",
          name: "Test Admin",
          password: hashedPassword,
          role: "ADMIN",
          isActive: true,
        },
      });

      expect(adminUser.lastLoginAt).toBeNull();

      // Simulate login update
      const loginTime = new Date();
      const updatedUser = await prisma.adminUser.update({
        where: { id: adminUser.id },
        data: { lastLoginAt: loginTime },
      });

      expect(updatedUser.lastLoginAt).toEqual(loginTime);
    });

    it("should not authenticate inactive admin user", async () => {
      const hashedPassword = await bcrypt.hash("testpassword", 12);
      await prisma.adminUser.create({
        data: {
          email: "<EMAIL>",
          name: "Inactive Admin",
          password: hashedPassword,
          role: "ADMIN",
          isActive: false,
        },
      });

      const foundUser = await prisma.adminUser.findUnique({
        where: { email: "<EMAIL>" },
      });

      expect(foundUser?.isActive).toBe(false);
      // In real authentication, this would be rejected
    });

    it("should handle moderator permissions", async () => {
      const hashedPassword = await bcrypt.hash("testpassword", 12);
      const permissions = {
        manage_products: true,
        manage_orders: true,
        manage_categories: false,
        view_analytics: true,
        manage_users: false,
      };

      const moderator = await prisma.adminUser.create({
        data: {
          email: "<EMAIL>",
          name: "Test Moderator",
          password: hashedPassword,
          role: "MODERATOR",
          isActive: true,
          permissions,
        },
      });

      expect(moderator.role).toBe("MODERATOR");
      expect(moderator.permissions).toEqual(permissions);
    });
  });

  describe("Admin Hierarchy", () => {
    it("should track admin creation hierarchy", async () => {
      const hashedPassword = await bcrypt.hash("testpassword", 12);

      // Create parent admin
      const parentAdmin = await prisma.adminUser.create({
        data: {
          email: "<EMAIL>",
          name: "Parent Admin",
          password: hashedPassword,
          role: "ADMIN",
          isActive: true,
        },
      });

      // Create child admin
      const childAdmin = await prisma.adminUser.create({
        data: {
          email: "<EMAIL>",
          name: "Child Admin",
          password: hashedPassword,
          role: "MODERATOR",
          isActive: true,
          createdBy: parentAdmin.id,
        },
      });

      // Verify relationship
      const adminWithCreator = await prisma.adminUser.findUnique({
        where: { id: childAdmin.id },
        include: {
          createdByAdmin: true,
        },
      });

      expect(adminWithCreator?.createdByAdmin?.id).toBe(parentAdmin.id);
      expect(adminWithCreator?.createdByAdmin?.name).toBe("Parent Admin");
    });
  });
});
