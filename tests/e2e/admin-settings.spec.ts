import { test, expect } from '@playwright/test';
import { AdminSettingsPage } from './pages/admin-settings.page';
import { loginAsAdmin, loginAsModerator } from './helpers/auth.helper';

test.describe('Admin Settings', () => {
  let settingsPage: AdminSettingsPage;

  test.describe('Admin User Access', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
      settingsPage = new AdminSettingsPage(page);
    });

    test('should load settings page successfully', async ({ page }) => {
      await settingsPage.goto();
      await expect(settingsPage.pageTitle).toBeVisible();
      await expect(settingsPage.saveButton).toBeVisible();
      await expect(settingsPage.refreshButton).toBeVisible();
      await expect(settingsPage.resetButton).toBeVisible();
    });

    test('should display all settings tabs', async ({ page }) => {
      await settingsPage.goto();
      
      await expect(settingsPage.generalTab).toBeVisible();
      await expect(settingsPage.contactTab).toBeVisible();
      await expect(settingsPage.paymentTab).toBeVisible();
      await expect(settingsPage.shippingTab).toBeVisible();
      await expect(settingsPage.emailTab).toBeVisible();
      await expect(settingsPage.notificationsTab).toBeVisible();
      await expect(settingsPage.seoTab).toBeVisible();
      await expect(settingsPage.securityTab).toBeVisible();
    });

    test('should navigate between tabs correctly', async ({ page }) => {
      await settingsPage.goto();
      
      // Test each tab navigation
      await settingsPage.clickTab('contact');
      await expect(page.getByText('Email liên hệ')).toBeVisible();
      
      await settingsPage.clickTab('payment');
      await expect(page.getByText('Phương thức thanh toán')).toBeVisible();
      
      await settingsPage.clickTab('shipping');
      await expect(page.getByText('Cài đặt vận chuyển')).toBeVisible();
      
      await settingsPage.clickTab('general');
      await expect(page.getByText('Tên website')).toBeVisible();
    });

    test('should update general settings successfully', async ({ page }) => {
      await settingsPage.goto();
      
      const testData = {
        siteName: 'NS Shop Updated Test',
        siteDescription: 'Updated test description for NS Shop',
        siteUrl: 'https://test.nsshop.com'
      };
      
      await settingsPage.updateGeneralSettings(testData);
      await settingsPage.saveSettings();
      await settingsPage.expectSuccessToast();
      
      // Verify settings were saved by refreshing and checking values
      await settingsPage.refreshSettings();
      await settingsPage.clickTab('general');
      
      await expect(settingsPage.siteNameInput).toHaveValue(testData.siteName);
      await expect(settingsPage.siteDescriptionTextarea).toHaveValue(testData.siteDescription);
      await expect(settingsPage.siteUrlInput).toHaveValue(testData.siteUrl);
    });

    test('should update contact settings successfully', async ({ page }) => {
      await settingsPage.goto();
      
      const testData = {
        email: '<EMAIL>',
        phone: '+84 123 456 789',
        address: '123 Test Street, Test City, Vietnam'
      };
      
      await settingsPage.updateContactSettings(testData);
      await settingsPage.saveSettings();
      await settingsPage.expectSuccessToast();
      
      // Verify settings persist after refresh
      await settingsPage.refreshSettings();
      await settingsPage.clickTab('contact');
      
      await expect(settingsPage.contactEmailInput).toHaveValue(testData.email);
      await expect(settingsPage.contactPhoneInput).toHaveValue(testData.phone);
      await expect(settingsPage.addressTextarea).toHaveValue(testData.address);
    });

    test('should handle payment method toggles', async ({ page }) => {
      await settingsPage.goto();
      await settingsPage.clickTab('payment');
      
      // Toggle payment methods
      await settingsPage.codCheckbox.check();
      await settingsPage.bankTransferCheckbox.check();
      await settingsPage.creditCardCheckbox.uncheck();
      
      await settingsPage.saveSettings();
      await settingsPage.expectSuccessToast();
      
      // Verify toggles persist
      await settingsPage.refreshSettings();
      await settingsPage.clickTab('payment');
      
      await expect(settingsPage.codCheckbox).toBeChecked();
      await expect(settingsPage.bankTransferCheckbox).toBeChecked();
      await expect(settingsPage.creditCardCheckbox).not.toBeChecked();
    });

    test('should update shipping settings', async ({ page }) => {
      await settingsPage.goto();
      await settingsPage.clickTab('shipping');
      
      await settingsPage.freeShippingThresholdInput.fill('600000');
      await settingsPage.shippingFeeInput.fill('35000');
      await settingsPage.estimatedDeliveryInput.fill('3-4 ngày');
      
      await settingsPage.saveSettings();
      await settingsPage.expectSuccessToast();
      
      // Verify values persist
      await settingsPage.refreshSettings();
      await settingsPage.clickTab('shipping');
      
      await expect(settingsPage.freeShippingThresholdInput).toHaveValue('600000');
      await expect(settingsPage.shippingFeeInput).toHaveValue('35000');
      await expect(settingsPage.estimatedDeliveryInput).toHaveValue('3-4 ngày');
    });

    test('should handle notification toggles', async ({ page }) => {
      await settingsPage.goto();
      await settingsPage.clickTab('notifications');
      
      // Toggle notification settings
      await settingsPage.orderNotificationsToggle.click();
      await settingsPage.stockAlertsToggle.click();
      await settingsPage.customerNotificationsToggle.click();
      
      await settingsPage.saveSettings();
      await settingsPage.expectSuccessToast();
    });

    test('should refresh settings correctly', async ({ page }) => {
      await settingsPage.goto();
      
      // Make some changes without saving
      await settingsPage.updateGeneralSettings({
        siteName: 'Temporary Change'
      });
      
      // Refresh should revert changes
      await settingsPage.refreshSettings();
      await settingsPage.clickTab('general');
      
      // Should not have the temporary change
      await expect(settingsPage.siteNameInput).not.toHaveValue('Temporary Change');
    });

    test('should reset to defaults', async ({ page }) => {
      await settingsPage.goto();
      
      // Make and save some changes
      await settingsPage.updateGeneralSettings({
        siteName: 'Changed Name'
      });
      await settingsPage.saveSettings();
      await settingsPage.expectSuccessToast();
      
      // Reset to defaults
      await settingsPage.resetToDefaults();
      
      // Should show default values
      await settingsPage.clickTab('general');
      await expect(settingsPage.siteNameInput).not.toHaveValue('Changed Name');
    });

    test('should handle validation errors', async ({ page }) => {
      await settingsPage.goto();
      
      // Try to save invalid email
      await settingsPage.updateContactSettings({
        email: 'invalid-email-format'
      });
      
      await settingsPage.saveSettings();
      
      // Should show error (depends on validation implementation)
      await expect(settingsPage.contactEmailInput).toHaveAttribute('aria-invalid', 'true');
    });

    test('should handle network errors gracefully', async ({ page }) => {
      // Intercept and fail the settings save request
      await page.route('/api/admin/settings', route => {
        route.abort('failed');
      });
      
      await settingsPage.goto();
      await settingsPage.updateGeneralSettings({
        siteName: 'Test Network Error'
      });
      
      await settingsPage.saveSettings();
      await settingsPage.expectErrorToast();
    });
  });

  test.describe('Moderator User Access', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsModerator(page);
      settingsPage = new AdminSettingsPage(page);
    });

    test('should restrict moderator access to settings', async ({ page }) => {
      // Attempt to access settings page as moderator
      await page.goto('/admin/settings');
      
      // Should be redirected or show access denied
      // This depends on your access control implementation
      await expect(page.getByText('Không có quyền truy cập')).toBeVisible();
    });
  });

  test.describe('Settings Persistence', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
      settingsPage = new AdminSettingsPage(page);
    });

    test('should persist settings across browser sessions', async ({ page, context }) => {
      await settingsPage.goto();
      
      const testSiteName = `Test Site ${Date.now()}`;
      await settingsPage.updateGeneralSettings({
        siteName: testSiteName
      });
      await settingsPage.saveSettings();
      await settingsPage.expectSuccessToast();
      
      // Create new page in same context (simulating new tab)
      const newPage = await context.newPage();
      const newSettingsPage = new AdminSettingsPage(newPage);
      await newSettingsPage.goto();
      await newSettingsPage.clickTab('general');
      
      // Settings should persist
      await expect(newSettingsPage.siteNameInput).toHaveValue(testSiteName);
      
      await newPage.close();
    });
  });
});
