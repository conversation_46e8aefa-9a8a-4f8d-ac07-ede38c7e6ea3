import { Page, Locator, expect } from '@playwright/test';

export class AdminLoginPage {
  readonly page: Page;
  readonly emailInput: Locator;
  readonly passwordInput: Locator;
  readonly loginButton: Locator;
  readonly errorMessage: Locator;
  readonly adminPortalTitle: Locator;
  readonly demoCredentials: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailInput = page.locator('#email');
    this.passwordInput = page.locator('#password');
    this.loginButton = page.getByRole('button', { name: '<PERSON><PERSON>ng nhập' });
    this.errorMessage = page.locator('[role="alert"]');
    this.adminPortalTitle = page.getByText('Admin Portal');
    this.demoCredentials = page.getByText('Tài khoản demo:');
    this.loadingSpinner = page.locator('.animate-spin');
  }

  async goto() {
    await this.page.goto('/admin/auth/signin');
    await expect(this.adminPortalTitle).toBeVisible();
  }

  async login(email: string, password: string) {
    await this.emailInput.fill(email);
    await this.passwordInput.fill(password);
    await this.loginButton.click();
  }

  async loginAsAdmin() {
    await this.login('<EMAIL>', 'admin123');
    await this.waitForNavigation();
  }

  async loginAsModerator() {
    await this.login('<EMAIL>', 'moderator123');
    await this.waitForNavigation();
  }

  async loginWithInvalidCredentials() {
    await this.login('<EMAIL>', 'wrongpassword');
  }

  async waitForNavigation() {
    // Wait for navigation to admin dashboard
    await this.page.waitForURL('/admin', { timeout: 10000 });
  }

  async waitForError() {
    await expect(this.errorMessage).toBeVisible({ timeout: 5000 });
  }

  async expectLoginFormVisible() {
    await expect(this.adminPortalTitle).toBeVisible();
    await expect(this.emailInput).toBeVisible();
    await expect(this.passwordInput).toBeVisible();
    await expect(this.loginButton).toBeVisible();
    await expect(this.demoCredentials).toBeVisible();
  }

  async expectErrorMessage(message: string) {
    await this.waitForError();
    await expect(this.errorMessage).toContainText(message);
  }

  async expectLoadingState() {
    await expect(this.loadingSpinner).toBeVisible();
    await expect(this.loginButton).toBeDisabled();
  }

  async expectFormValidation() {
    // Test empty form submission
    await this.loginButton.click();
    
    // Check HTML5 validation
    await expect(this.emailInput).toHaveAttribute('required');
    await expect(this.passwordInput).toHaveAttribute('required');
  }

  async fillInvalidEmail() {
    await this.emailInput.fill('invalid-email');
    await this.passwordInput.fill('somepassword');
    await this.loginButton.click();
    
    // Check HTML5 email validation
    const emailValidity = await this.emailInput.evaluate((el: HTMLInputElement) => el.validity.valid);
    expect(emailValidity).toBe(false);
  }

  async expectDemoCredentialsVisible() {
    await expect(this.demoCredentials).toBeVisible();
    await expect(this.page.getByText('Admin: <EMAIL> / admin123')).toBeVisible();
    await expect(this.page.getByText('Moderator: <EMAIL> / moderator123')).toBeVisible();
  }
}
