import { Page, Locator, expect } from '@playwright/test';

export class AdminSettingsPage {
  readonly page: Page;
  readonly pageTitle: Locator;
  readonly saveButton: Locator;
  readonly refreshButton: Locator;
  readonly resetButton: Locator;
  readonly loadingIndicator: Locator;
  readonly successToast: Locator;
  readonly errorToast: Locator;

  // Tab navigation
  readonly generalTab: Locator;
  readonly contactTab: Locator;
  readonly paymentTab: Locator;
  readonly shippingTab: Locator;
  readonly emailTab: Locator;
  readonly notificationsTab: Locator;
  readonly seoTab: Locator;
  readonly securityTab: Locator;

  // General settings
  readonly siteNameInput: Locator;
  readonly siteDescriptionTextarea: Locator;
  readonly siteUrlInput: Locator;

  // Contact settings
  readonly contactEmailInput: Locator;
  readonly contactPhoneInput: Locator;
  readonly addressTextarea: Locator;

  // Payment settings
  readonly codCheckbox: Locator;
  readonly bankTransferCheckbox: Locator;
  readonly creditCardCheckbox: Locator;

  // Shipping settings
  readonly freeShippingThresholdInput: Locator;
  readonly shippingFeeInput: Locator;
  readonly estimatedDeliveryInput: Locator;

  // Notification settings
  readonly orderNotificationsToggle: Locator;
  readonly stockAlertsToggle: Locator;
  readonly customerNotificationsToggle: Locator;

  constructor(page: Page) {
    this.page = page;
    this.pageTitle = page.getByText('Cài đặt hệ thống');
    this.saveButton = page.getByRole('button', { name: /Lưu cài đặt|Đang lưu.../ });
    this.refreshButton = page.getByRole('button', { name: 'Làm mới' });
    this.resetButton = page.getByRole('button', { name: 'Khôi phục mặc định' });
    this.loadingIndicator = page.locator('.animate-pulse');
    this.successToast = page.locator('[data-sonner-toast][data-type="success"]');
    this.errorToast = page.locator('[data-sonner-toast][data-type="error"]');

    // Tabs
    this.generalTab = page.getByRole('button', { name: 'Tổng quan' });
    this.contactTab = page.getByRole('button', { name: 'Liên hệ' });
    this.paymentTab = page.getByRole('button', { name: 'Thanh toán' });
    this.shippingTab = page.getByRole('button', { name: 'Vận chuyển' });
    this.emailTab = page.getByRole('button', { name: 'Email' });
    this.notificationsTab = page.getByRole('button', { name: 'Thông báo' });
    this.seoTab = page.getByRole('button', { name: 'SEO' });
    this.securityTab = page.getByRole('button', { name: 'Bảo mật' });

    // General settings inputs
    this.siteNameInput = page.locator('input').filter({ hasText: /Tên website/ }).or(
      page.locator('label:has-text("Tên website") + input')
    ).or(
      page.locator('input[value*="NS Shop"]').first()
    );
    this.siteDescriptionTextarea = page.locator('textarea').filter({ hasText: /Mô tả website/ }).or(
      page.locator('label:has-text("Mô tả website") + textarea')
    );
    this.siteUrlInput = page.locator('input[type="url"]').or(
      page.locator('label:has-text("URL website") + input')
    );

    // Contact settings
    this.contactEmailInput = page.locator('input[type="email"]').or(
      page.locator('label:has-text("Email liên hệ") + input')
    );
    this.contactPhoneInput = page.locator('input[type="tel"]').or(
      page.locator('label:has-text("Số điện thoại") + input')
    );
    this.addressTextarea = page.locator('label:has-text("Địa chỉ") + textarea');

    // Payment settings
    this.codCheckbox = page.locator('input[type="checkbox"]').filter({ hasText: /COD|Thanh toán khi nhận hàng/ });
    this.bankTransferCheckbox = page.locator('input[type="checkbox"]').filter({ hasText: /Chuyển khoản/ });
    this.creditCardCheckbox = page.locator('input[type="checkbox"]').filter({ hasText: /Thẻ tín dụng/ });

    // Shipping settings
    this.freeShippingThresholdInput = page.locator('input[type="number"]').filter({ hasText: /Miễn phí vận chuyển/ });
    this.shippingFeeInput = page.locator('input[type="number"]').filter({ hasText: /Phí vận chuyển/ });
    this.estimatedDeliveryInput = page.locator('input').filter({ hasText: /Thời gian giao hàng/ });

    // Notification settings
    this.orderNotificationsToggle = page.locator('[role="switch"]').filter({ hasText: /Thông báo đơn hàng/ });
    this.stockAlertsToggle = page.locator('[role="switch"]').filter({ hasText: /Cảnh báo tồn kho/ });
    this.customerNotificationsToggle = page.locator('[role="switch"]').filter({ hasText: /Thông báo khách hàng/ });
  }

  async goto() {
    await this.page.goto('/admin/settings');
    await expect(this.pageTitle).toBeVisible();
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    // Wait for loading to complete
    await this.page.waitForLoadState('networkidle');
    await expect(this.loadingIndicator).not.toBeVisible({ timeout: 10000 });
  }

  async clickTab(tabName: 'general' | 'contact' | 'payment' | 'shipping' | 'email' | 'notifications' | 'seo' | 'security') {
    const tabMap = {
      general: this.generalTab,
      contact: this.contactTab,
      payment: this.paymentTab,
      shipping: this.shippingTab,
      email: this.emailTab,
      notifications: this.notificationsTab,
      seo: this.seoTab,
      security: this.securityTab,
    };

    await tabMap[tabName].click();
    await this.page.waitForTimeout(500); // Wait for tab content to load
  }

  async updateGeneralSettings(settings: { siteName?: string; siteDescription?: string; siteUrl?: string }) {
    await this.clickTab('general');
    
    if (settings.siteName) {
      await this.siteNameInput.fill(settings.siteName);
    }
    if (settings.siteDescription) {
      await this.siteDescriptionTextarea.fill(settings.siteDescription);
    }
    if (settings.siteUrl) {
      await this.siteUrlInput.fill(settings.siteUrl);
    }
  }

  async updateContactSettings(settings: { email?: string; phone?: string; address?: string }) {
    await this.clickTab('contact');
    
    if (settings.email) {
      await this.contactEmailInput.fill(settings.email);
    }
    if (settings.phone) {
      await this.contactPhoneInput.fill(settings.phone);
    }
    if (settings.address) {
      await this.addressTextarea.fill(settings.address);
    }
  }

  async saveSettings() {
    await this.saveButton.click();
    await this.waitForSaveComplete();
  }

  async waitForSaveComplete() {
    // Wait for save to complete
    await expect(this.saveButton).not.toHaveText('Đang lưu...', { timeout: 10000 });
    await this.page.waitForTimeout(1000); // Additional wait for toast
  }

  async expectSuccessToast() {
    await expect(this.successToast).toBeVisible({ timeout: 5000 });
  }

  async expectErrorToast() {
    await expect(this.errorToast).toBeVisible({ timeout: 5000 });
  }

  async refreshSettings() {
    await this.refreshButton.click();
    await this.waitForPageLoad();
  }

  async resetToDefaults() {
    await this.resetButton.click();
    await this.waitForPageLoad();
  }
}
