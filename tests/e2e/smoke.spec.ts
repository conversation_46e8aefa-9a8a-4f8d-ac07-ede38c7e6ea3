import { test, expect } from '@playwright/test';

test.describe('Smoke Tests', () => {
  test('should load the application homepage', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveTitle(/NS Shop/);
  });

  test('should navigate to admin login page', async ({ page }) => {
    await page.goto('/admin/auth/signin');
    await expect(page.getByText('Admin Portal')).toBeVisible();
    await expect(page.getByText('Đăng nhập vào hệ thống quản trị NS Shop')).toBeVisible();
  });

  test('should show demo credentials on login page', async ({ page }) => {
    await page.goto('/admin/auth/signin');
    await expect(page.getByText('Tài khoản demo:')).toBeVisible();
    await expect(page.getByText('Admin: <EMAIL> / admin123')).toBeVisible();
    await expect(page.getByText('Moderator: <EMAIL> / moderator123')).toBeVisible();
  });
});
