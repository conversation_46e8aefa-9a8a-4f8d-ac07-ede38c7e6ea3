# E2E Tests with <PERSON><PERSON>

This directory contains end-to-end tests for the NS Shop admin system using <PERSON>wright.

## Overview

The E2E tests cover:

- Admin login functionality
- Settings management
- Complete admin workflows
- Access control and permissions
- Error handling and edge cases

## Test Structure

```
tests/e2e/
├── README.md                 # This file
├── global-setup.ts          # Global test setup (database, users)
├── global-teardown.ts       # Global test cleanup
├── pages/                   # Page Object Models
│   ├── admin-login.page.ts  # Login page interactions
│   └── admin-settings.page.ts # Settings page interactions
├── helpers/                 # Test helper functions
│   └── auth.helper.ts       # Authentication utilities
└── *.spec.ts               # Test files
    ├── admin-login.spec.ts     # Login functionality tests
    ├── admin-settings.spec.ts  # Settings management tests
    └── admin-workflow.spec.ts  # Complete workflow tests
```

## Test Users

The tests use predefined test users created during global setup:

- **Admin User**: `<EMAIL>` / `admin123`
  - Full admin permissions
  - Can access all admin features including settings

- **Moderator User**: `<EMAIL>` / `moderator123`
  - Limited permissions
  - Cannot access admin management or settings

- **Test Users**: Additional test users for specific scenarios
  - `<EMAIL>` / `testpassword123`
  - `<EMAIL>` / `testpassword123`

## Running Tests

### Prerequisites

1. Ensure the development server is running:

   ```bash
   npm run dev
   ```

2. Ensure the database is set up and seeded:

   ```bash
   npm run db:setup-new
   ```

3. Install Playwright browsers (if not already done):
   ```bash
   npx playwright install
   ```

### Quick Start

Run a smoke test to verify everything is working:

```bash
npx playwright test smoke
```

### Run All E2E Tests

```bash
npm run test:e2e
```

### Run Tests with UI (Interactive Mode)

```bash
npm run test:e2e:ui
```

### Run Specific Test Files

```bash
# Smoke tests (quick verification)
npx playwright test smoke

# Login tests only
npx playwright test admin-login

# Settings tests only
npx playwright test admin-settings

# Workflow tests only
npx playwright test admin-workflow
```

### Run Tests in Different Browsers

```bash
# Chrome only
npx playwright test --project=chromium

# Firefox only
npx playwright test --project=firefox

# Safari only
npx playwright test --project=webkit

# All browsers
npx playwright test
```

### Run Tests in Headed Mode (with browser UI)

```bash
npm run test:e2e:headed
```

### Debug Tests

```bash
# Run with debug mode
npm run test:e2e:debug

# Run specific test with debug
npx playwright test admin-login --debug
```

### View Test Reports

```bash
npm run test:e2e:report
```

## Test Configuration

The tests are configured in `playwright.config.ts` with:

- **Base URL**: `http://localhost:3000` (configurable via `PLAYWRIGHT_BASE_URL`)
- **Browsers**: Chrome, Firefox, Safari, Mobile Chrome, Mobile Safari
- **Retries**: 2 retries on CI, 0 locally
- **Timeouts**: 30s test timeout, 10s action timeout
- **Screenshots**: On failure only
- **Videos**: On failure only
- **Traces**: On first retry

## Page Object Models

### AdminLoginPage

Handles all login page interactions:

- Form filling and submission
- Error message validation
- Loading state verification
- Navigation after login

### AdminSettingsPage

Handles settings page interactions:

- Tab navigation
- Form updates across different settings categories
- Save/refresh/reset operations
- Toast notification verification

## Helper Functions

### Authentication Helpers

- `loginAsAdmin(page)`: Login with admin credentials
- `loginAsModerator(page)`: Login with moderator credentials
- `loginWithCredentials(page, email, password)`: Login with custom credentials
- `ensureLoggedOut(page)`: Ensure user is logged out
- `createAdminContext(browser)`: Create authenticated browser context

## Test Categories

### Login Tests (`admin-login.spec.ts`)

- **Form Display**: Login form rendering and validation
- **Successful Login**: Valid credential scenarios
- **Failed Login**: Invalid credentials and error handling
- **Form Interaction**: Loading states, keyboard navigation
- **Security**: Inactive users, rate limiting
- **Accessibility**: ARIA labels, keyboard navigation

### Settings Tests (`admin-settings.spec.ts`)

- **Admin Access**: Full settings access for admin users
- **Moderator Access**: Restricted access for moderator users
- **Settings Updates**: All settings categories (general, contact, payment, etc.)
- **Persistence**: Settings saved and loaded correctly
- **Error Handling**: Network errors, validation errors

### Workflow Tests (`admin-workflow.spec.ts`)

- **Complete Workflow**: End-to-end admin login and settings update
- **Session Management**: Session persistence across navigation
- **Concurrent Updates**: Multiple tabs/users updating settings
- **Browser Refresh**: State persistence after refresh
- **Network Issues**: Graceful handling of network interruptions

## Best Practices

1. **Page Object Models**: Use page objects for reusable interactions
2. **Test Isolation**: Each test starts from a clean state
3. **Explicit Waits**: Use proper waiting strategies for dynamic content
4. **Error Scenarios**: Test both happy path and error conditions
5. **Accessibility**: Include accessibility checks where relevant
6. **Mobile Testing**: Tests run on both desktop and mobile viewports

## Debugging Tips

1. **Screenshots**: Check `test-results/` for failure screenshots
2. **Videos**: Review failure videos for visual debugging
3. **Traces**: Use Playwright trace viewer for detailed debugging
4. **Console Logs**: Check browser console for JavaScript errors
5. **Network Tab**: Monitor network requests for API issues

## Environment Variables

- `PLAYWRIGHT_BASE_URL`: Base URL for tests (default: http://localhost:3000)
- `CI`: Enables CI-specific configuration (retries, workers)

## Maintenance

- Update test users in `global-setup.ts` if authentication changes
- Update page objects if UI components change
- Add new test scenarios for new features
- Keep test data isolated and clean up after tests

## Troubleshooting

### Common Issues

1. **Tests timing out**: Increase timeouts in config or use better waiting strategies
2. **Element not found**: Update selectors in page objects
3. **Database issues**: Ensure database is properly seeded
4. **Network errors**: Check if development server is running
5. **Authentication failures**: Verify test user credentials in global setup
