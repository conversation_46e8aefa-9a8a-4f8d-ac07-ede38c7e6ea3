import { test, expect } from '@playwright/test';
import { AdminLoginPage } from './pages/admin-login.page';
import { AdminSettingsPage } from './pages/admin-settings.page';
import { ensureLoggedOut } from './helpers/auth.helper';

test.describe('Complete Admin Workflow', () => {
  test('should complete full admin login and settings update workflow', async ({ page }) => {
    // Start from logged out state
    await ensureLoggedOut(page);
    
    // Step 1: Navigate to admin login
    const loginPage = new AdminLoginPage(page);
    await loginPage.goto();
    await loginPage.expectLoginFormVisible();
    
    // Step 2: Login as admin
    await loginPage.loginAsAdmin();
    await expect(page).toHaveURL('/admin');
    
    // Step 3: Navigate to settings
    const settingsPage = new AdminSettingsPage(page);
    await settingsPage.goto();
    await settingsPage.waitForPageLoad();
    
    // Step 4: Update multiple settings across different tabs
    const timestamp = Date.now();
    
    // Update general settings
    await settingsPage.updateGeneralSettings({
      siteName: `NS Shop Workflow Test ${timestamp}`,
      siteDescription: `Workflow test description ${timestamp}`,
      siteUrl: 'https://workflow-test.nsshop.com'
    });
    
    // Update contact settings
    await settingsPage.updateContactSettings({
      email: `workflow-test-${timestamp}@nsshop.com`,
      phone: '+84 987 654 321',
      address: `${timestamp} Workflow Test Street, Test City`
    });
    
    // Update payment settings
    await settingsPage.clickTab('payment');
    await settingsPage.codCheckbox.check();
    await settingsPage.bankTransferCheckbox.check();
    
    // Update shipping settings
    await settingsPage.clickTab('shipping');
    await settingsPage.freeShippingThresholdInput.fill('750000');
    await settingsPage.shippingFeeInput.fill('40000');
    
    // Step 5: Save all settings
    await settingsPage.saveSettings();
    await settingsPage.expectSuccessToast();
    
    // Step 6: Verify settings were saved by refreshing and checking
    await settingsPage.refreshSettings();
    
    // Verify general settings
    await settingsPage.clickTab('general');
    await expect(settingsPage.siteNameInput).toHaveValue(`NS Shop Workflow Test ${timestamp}`);
    await expect(settingsPage.siteDescriptionTextarea).toHaveValue(`Workflow test description ${timestamp}`);
    
    // Verify contact settings
    await settingsPage.clickTab('contact');
    await expect(settingsPage.contactEmailInput).toHaveValue(`workflow-test-${timestamp}@nsshop.com`);
    await expect(settingsPage.contactPhoneInput).toHaveValue('+84 987 654 321');
    
    // Verify payment settings
    await settingsPage.clickTab('payment');
    await expect(settingsPage.codCheckbox).toBeChecked();
    await expect(settingsPage.bankTransferCheckbox).toBeChecked();
    
    // Verify shipping settings
    await settingsPage.clickTab('shipping');
    await expect(settingsPage.freeShippingThresholdInput).toHaveValue('750000');
    await expect(settingsPage.shippingFeeInput).toHaveValue('40000');
    
    // Step 7: Test navigation to other admin sections
    await page.goto('/admin');
    await expect(page.getByText('Dashboard')).toBeVisible();
    
    // Navigate back to settings to ensure everything still works
    await settingsPage.goto();
    await settingsPage.clickTab('general');
    await expect(settingsPage.siteNameInput).toHaveValue(`NS Shop Workflow Test ${timestamp}`);
  });

  test('should handle moderator workflow with limited access', async ({ page }) => {
    await ensureLoggedOut(page);
    
    // Login as moderator
    const loginPage = new AdminLoginPage(page);
    await loginPage.goto();
    await loginPage.loginAsModerator();
    await expect(page).toHaveURL('/admin');
    
    // Try to access settings (should be restricted)
    await page.goto('/admin/settings');
    
    // Should show access denied or redirect
    // This depends on your access control implementation
    const hasAccessDenied = await page.getByText('Không có quyền truy cập').isVisible().catch(() => false);
    const isRedirected = page.url() !== '/admin/settings';
    
    expect(hasAccessDenied || isRedirected).toBe(true);
  });

  test('should maintain session across multiple page navigations', async ({ page }) => {
    await ensureLoggedOut(page);
    
    const loginPage = new AdminLoginPage(page);
    await loginPage.goto();
    await loginPage.loginAsAdmin();
    
    // Navigate through multiple admin pages
    const adminPages = [
      '/admin',
      '/admin/settings',
      '/admin/admins',
      '/admin/settings'
    ];
    
    for (const adminPage of adminPages) {
      await page.goto(adminPage);
      
      // Should not be redirected to login
      expect(page.url()).not.toContain('/auth/signin');
      
      // Should have admin navigation visible
      await expect(page.locator('nav')).toBeVisible();
    }
  });

  test('should handle concurrent settings updates', async ({ page, context }) => {
    await ensureLoggedOut(page);
    
    const loginPage = new AdminLoginPage(page);
    await loginPage.goto();
    await loginPage.loginAsAdmin();
    
    // Open settings in first tab
    const settingsPage1 = new AdminSettingsPage(page);
    await settingsPage1.goto();
    
    // Open settings in second tab
    const page2 = await context.newPage();
    await page2.goto('/admin/settings');
    const settingsPage2 = new AdminSettingsPage(page2);
    await settingsPage2.waitForPageLoad();
    
    // Make different changes in each tab
    await settingsPage1.updateGeneralSettings({
      siteName: 'Tab 1 Update'
    });
    
    await settingsPage2.updateGeneralSettings({
      siteDescription: 'Tab 2 Update'
    });
    
    // Save from first tab
    await settingsPage1.saveSettings();
    await settingsPage1.expectSuccessToast();
    
    // Save from second tab
    await settingsPage2.saveSettings();
    await settingsPage2.expectSuccessToast();
    
    // Refresh both tabs and verify final state
    await settingsPage1.refreshSettings();
    await settingsPage2.refreshSettings();
    
    await settingsPage1.clickTab('general');
    await settingsPage2.clickTab('general');
    
    // Both should show the latest saved values
    // The exact behavior depends on your conflict resolution strategy
    const siteName1 = await settingsPage1.siteNameInput.inputValue();
    const siteName2 = await settingsPage2.siteNameInput.inputValue();
    expect(siteName1).toBe(siteName2);
    
    await page2.close();
  });

  test('should handle browser refresh during settings update', async ({ page }) => {
    await ensureLoggedOut(page);
    
    const loginPage = new AdminLoginPage(page);
    await loginPage.goto();
    await loginPage.loginAsAdmin();
    
    const settingsPage = new AdminSettingsPage(page);
    await settingsPage.goto();
    
    // Make some changes
    const testName = `Refresh Test ${Date.now()}`;
    await settingsPage.updateGeneralSettings({
      siteName: testName
    });
    
    // Save settings
    await settingsPage.saveSettings();
    await settingsPage.expectSuccessToast();
    
    // Refresh the browser
    await page.reload();
    await settingsPage.waitForPageLoad();
    
    // Should still be logged in and settings should persist
    await settingsPage.clickTab('general');
    await expect(settingsPage.siteNameInput).toHaveValue(testName);
  });

  test('should handle network interruption gracefully', async ({ page }) => {
    await ensureLoggedOut(page);
    
    const loginPage = new AdminLoginPage(page);
    await loginPage.goto();
    await loginPage.loginAsAdmin();
    
    const settingsPage = new AdminSettingsPage(page);
    await settingsPage.goto();
    
    // Simulate network failure during save
    await page.route('/api/admin/settings', route => {
      route.abort('failed');
    });
    
    await settingsPage.updateGeneralSettings({
      siteName: 'Network Test'
    });
    
    await settingsPage.saveSettings();
    await settingsPage.expectErrorToast();
    
    // Remove network failure simulation
    await page.unroute('/api/admin/settings');
    
    // Try saving again - should work
    await settingsPage.saveSettings();
    await settingsPage.expectSuccessToast();
  });
});
